(['/Users/<USER>/Documents/hackathon/go/run.py'],
 ['/Users/<USER>/Documents/hackathon/go'],
 [],
 [('/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.8 (v3.12.8:2dc476bcb91, Dec  3 2024, 14:43:19) [Clang 13.0.0 '
 '(clang-1300.0.29.30)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('run', '/Users/<USER>/Documents/hackathon/go/run.py', 'PYSOURCE')],
 [('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('struct',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/struct.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.12/Python',
   '/Library/Frameworks/Python.framework/Versions/3.12/Python',
   'BINARY'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/libcrypto.3.dylib',
   'BINARY')],
 [],
 [],
 [('Python', 'Python.framework/Versions/3.12/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/Documents/hackathon/go/build/WebTester/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.12/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.12/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.12', 'SYMLINK')],
 [('operator',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/operator.py',
   'PYMODULE'),
  ('types',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/types.py',
   'PYMODULE'),
  ('traceback',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/traceback.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/encodings/__init__.py',
   'PYMODULE'),
  ('sre_parse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sre_parse.py',
   'PYMODULE'),
  ('sre_constants',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sre_constants.py',
   'PYMODULE'),
  ('re._parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/re/_parser.py',
   'PYMODULE'),
  ('re._constants',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/re/_constants.py',
   'PYMODULE'),
  ('re._compiler',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/re/_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/re/_casefix.py',
   'PYMODULE'),
  ('re',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/re/__init__.py',
   'PYMODULE'),
  ('abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/abc.py',
   'PYMODULE'),
  ('collections.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/collections/abc.py',
   'PYMODULE'),
  ('collections',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/collections/__init__.py',
   'PYMODULE'),
  ('functools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/functools.py',
   'PYMODULE'),
  ('heapq',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/heapq.py',
   'PYMODULE'),
  ('keyword',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/keyword.py',
   'PYMODULE'),
  ('enum',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/enum.py',
   'PYMODULE'),
  ('sre_compile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sre_compile.py',
   'PYMODULE'),
  ('stat',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stat.py',
   'PYMODULE'),
  ('_weakrefset',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_weakrefset.py',
   'PYMODULE'),
  ('genericpath',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/genericpath.py',
   'PYMODULE'),
  ('linecache',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/linecache.py',
   'PYMODULE'),
  ('copyreg',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copyreg.py',
   'PYMODULE'),
  ('posixpath',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/posixpath.py',
   'PYMODULE'),
  ('io',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/io.py',
   'PYMODULE'),
  ('_collections_abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_collections_abc.py',
   'PYMODULE'),
  ('codecs',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/codecs.py',
   'PYMODULE'),
  ('locale',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/locale.py',
   'PYMODULE'),
  ('ntpath',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ntpath.py',
   'PYMODULE'),
  ('warnings',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/warnings.py',
   'PYMODULE'),
  ('weakref',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/weakref.py',
   'PYMODULE'),
  ('reprlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/reprlib.py',
   'PYMODULE'),
  ('os',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/os.py',
   'PYMODULE')])
