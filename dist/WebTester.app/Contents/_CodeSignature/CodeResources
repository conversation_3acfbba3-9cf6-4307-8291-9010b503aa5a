<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/base_library.zip</key>
		<data>
		CPF5HTCuJUq3edfWPPf1uQJGL1o=
		</data>
		<key>Resources/icon-windowed.icns</key>
		<data>
		eEHOuYpZLB0vKGVIWGZOh5rH8+o=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.12/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			McQBZgude/iV+NZo2YlwaqgrwI8=
			</data>
			<key>requirement</key>
			<string>cdhash H"31c401660b9d7bf895f8d668d989706aa82bc08f"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OWHh/98QR2mmyfdMt6SBxK0i/GE=
			</data>
			<key>requirement</key>
			<string>cdhash H"3961e1ffdf104769a6c9f74cb7a481c4ad22fc61"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			tx0hndH6TvElyZu3KDVPK9XgIfs=
			</data>
			<key>requirement</key>
			<string>cdhash H"b71d219dd1fa4ef125c99bb728354f2bd5e021fb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			shifkUEi81cFO6L2fkXKGa9lWx4=
			</data>
			<key>requirement</key>
			<string>cdhash H"b2189f914122f357053ba2f67e45ca19af655b1e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BcvLEOvrUXCGN1BGrfdTBLOmej8=
			</data>
			<key>requirement</key>
			<string>cdhash H"05cbcb10ebeb517086375046adf75304b3a67a3f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YPjBddZm69FKWAR7O/u8k+VuxZg=
			</data>
			<key>requirement</key>
			<string>cdhash H"60f8c175d666ebd14a58047b3bfbbc93e56ec598"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			e+AMGWJfckgMEasvxT/6aShIbFQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"7be00c19625f72480c11ab2fc53ffa6928486c54"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			RUu9AfEP1vn9Rn4/mmRv4avgsX0=
			</data>
			<key>requirement</key>
			<string>cdhash H"454bbd01f10fd6f9fd467e3f9a646fe1abe0b17d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			USK9O0Z1knmEbqp6fbTrcZYzdYE=
			</data>
			<key>requirement</key>
			<string>cdhash H"5122bd3b46759279846eaa7a7db4eb7196337581"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VRY95vQ2OG1aTXwI2u6neEfplQo=
			</data>
			<key>requirement</key>
			<string>cdhash H"55163de6f436386d5a4d7c08daeea77847e9950a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3JsSbJSuccjVJy6t0B8gVx6Phuk=
			</data>
			<key>requirement</key>
			<string>cdhash H"dc9b126c94ae71c8d5272eadd01f20571e8f86e9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			F3stsfmrE0LCTi9xzxyw3TNcIJU=
			</data>
			<key>requirement</key>
			<string>cdhash H"177b2db1f9ab1342c24e2f71cf1cb0dd335c2095"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			bAf18omRUCziZ218MgX+BttpcDA=
			</data>
			<key>requirement</key>
			<string>cdhash H"6c07f5f28991502ce2676d7c3205fe06db697030"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ie1xwUktdTirUpEXOAQlrc/+RkQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"21ed71c1492d7538ab529117380425adcffe4644"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SMOnMVWj0rL81/kZ6t155sB0MHM=
			</data>
			<key>requirement</key>
			<string>cdhash H"48c3a73155a3d2b2fcd7f919eadd79e6c0743073"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Dk+VHQRnyJghFjoeohriFJfBJF8=
			</data>
			<key>requirement</key>
			<string>cdhash H"0e4f951d0467c89821163a1ea21ae21497c1245f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wDCgzWRIe0f0gli105aE0AH1JZM=
			</data>
			<key>requirement</key>
			<string>cdhash H"c030a0cd64487b47f48258b5d39684d001f52593"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UFW6jLcEc0Xq7lAVtrzOcsC548w=
			</data>
			<key>requirement</key>
			<string>cdhash H"5055ba8cb7047345eaee5015b6bcce72c0b9e3cc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pOb4FcX7UEFoc9Tymcz4zuKnqvE=
			</data>
			<key>requirement</key>
			<string>cdhash H"a4e6f815c5fb50416873d4f299ccf8cee2a7aaf1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1wdjH4DDRFNYC4f8jPLTdEzaxnQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"d707631f80c34453580b87fc8cf2d3744cdac674"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			WQONjE/hHrdNI6/poulMquYAMY0=
			</data>
			<key>requirement</key>
			<string>cdhash H"59038d8c4fe11eb74d23afe9a2e94caae600318d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			b8MU7+DTuiED7Tr2m+aeUBGMR5w=
			</data>
			<key>requirement</key>
			<string>cdhash H"6fc314efe0d3ba2103ed3af69be69e50118c479c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			N44loBfEFKeSD0/9ayn3zR+DDvQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"378e25a017c414a7920f4ffd6b29f7cd1f830ef4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Jgge6MMfWEFjfac/MPVeKgQoo4Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"26081ee8c31f5841637da73f30f55e2a0428a384"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha2.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			2H54cYXJtAF+7RxLDu7zS1hBtIE=
			</data>
			<key>requirement</key>
			<string>cdhash H"d87e787185c9b4017eed1c4b0eeef34b5841b481"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			8av7+EZCnz9KbPN6PU/alWNBlQQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"f1abfbf846429f3f4a6cf37a3d4fda9563419504"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QqrQgddiks1kBUvVE3egx2E19NM=
			</data>
			<key>requirement</key>
			<string>cdhash H"42aad081d76292cd64054bd51377a0c76135f4d3"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QZ5DR7aXuZnnJDvM/bc192xbQtk=
			</data>
			<key>requirement</key>
			<string>cdhash H"419e4347b697b999e7243bccfdb735f76c5b42d9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			7pCOeYiS717HIhzYcMXvtw2bbeQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"ee908e798892ef5ec7221cd870c5efb70d9b6de4"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			w+xdodmublhsT2hgqwxoK40HrG4=
			</data>
			<key>requirement</key>
			<string>cdhash H"c3ec5da1d9ae6e586c4f6860ab0c682b8d07ac6e"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YT4LBNHb5yfsRvyvy33qi1ZF2ho=
			</data>
			<key>requirement</key>
			<string>cdhash H"613e0b04d1dbe727ec46fcafcb7dea8b5645da1a"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			P7HCyEwooW4DF0Dfo3BIdIcoxFo=
			</data>
			<key>requirement</key>
			<string>cdhash H"3fb1c2c84c28a16e031740dfa37048748728c45a"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			W2HplbbJQPOinwGARVtv+3xKA5E=
			</data>
			<key>requirement</key>
			<string>cdhash H"5b61e995b6c940f3a29f0180455b6ffb7c4a0391"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			r4SERwjIurbZc/L3YgQBEWSqTGI=
			</data>
			<key>requirement</key>
			<string>cdhash H"af84844708c8bab6d973f2f76204011164aa4c62"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ezYiG/e+h35WFJrhbZ9n/6v0aVA=
			</data>
			<key>requirement</key>
			<string>cdhash H"7b36221bf7be877e56149ae16d9f67ffabf46950"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			x6dvfgoiFljoOI2jy9NcUj9ethc=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7a76f7e0a221658e8388da3cbd35c523f5eb617"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3E72OP/Xn/EVRcbGGK0jm/Qh+vU=
			</data>
			<key>requirement</key>
			<string>cdhash H"dc4ef638ffd79ff11545c6c618ad239bf421faf5"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Yn/Q/rodgf/NmPqes6+t2UmB6ds=
			</data>
			<key>requirement</key>
			<string>cdhash H"627fd0feba1d81ffcd98fa9eb3afadd94981e9db"</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lyiqOhfaHzNZ8zKivO3UKEZj7vs=
			</data>
			<key>requirement</key>
			<string>cdhash H"9728aa3a17da1f3359f332a2bcedd4284663eefb"</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.12/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			mU9impdEdSpPY+zUIfRn11YWyCJ73l+Cb6FqkMIgXR4=
			</data>
		</dict>
		<key>Resources/icon-windowed.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			uQo7VuWRab4Phv4EEGmfQsyqFqDIXZgO8OtgaAMvCzY=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
