('/Users/<USER>/Documents/hackathon/go/build/WebTester/WebTester',
 True,
 False,
 True,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 'arm64',
 None,
 None,
 '/Users/<USER>/Documents/hackathon/go/build/WebTester/WebTester.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Documents/hackathon/go/build/WebTester/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Documents/hackathon/go/build/WebTester/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Documents/hackathon/go/build/WebTester/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Documents/hackathon/go/build/WebTester/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Documents/hackathon/go/build/WebTester/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('run', '/Users/<USER>/Documents/hackathon/go/run.py', 'PYSOURCE')],
 [],
 False,
 False,
 1752933943,
 [('run',
   '/Users/<USER>/Documents/hackathon/go/env/lib/python3.12/site-packages/PyInstaller/bootloader/Darwin-64bit/run',
   'EXECUTABLE')],
 '/Library/Frameworks/Python.framework/Versions/3.12/Python')
