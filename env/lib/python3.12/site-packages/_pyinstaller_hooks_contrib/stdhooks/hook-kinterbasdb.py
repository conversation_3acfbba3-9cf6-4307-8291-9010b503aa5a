# ------------------------------------------------------------------
# Copyright (c) 2020 PyInstaller Development Team.
#
# This file is distributed under the terms of the GNU General Public
# License (version 2.0 or later).
#
# The full license is available in LICENSE, distributed with
# this software.
#
# SPDX-License-Identifier: GPL-2.0-or-later
# ------------------------------------------------------------------

# kinterbasdb
hiddenimports = ['k_exceptions', 'services', 'typeconv_naked',
                 'typeconv_backcompat', 'typeconv_23plus',
                 'typeconv_datetime_stdlib', 'typeconv_datetime_mx',
                 'typeconv_datetime_naked', 'typeconv_fixed_fixedpoint',
                 'typeconv_fixed_stdlib',  'typeconv_text_unicode',
                 'typeconv_util_isinstance', '_kinterbasdb', '_kiservices']
