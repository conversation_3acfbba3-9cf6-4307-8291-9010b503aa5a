('/Users/<USER>/Documents/hackathon/go/build/WebTester/PYZ-00.pyz',
 [('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE')])
